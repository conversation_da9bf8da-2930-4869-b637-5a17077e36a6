import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';

import { Loading } from '@/shared/components';
import MainLayout from '@/shared/layouts/MainLayout';

// Import pages with lazy loading
const HrmHomePage = lazy(() => import('@/modules/hrm/pages/HrmHomePage'));
const EmployeesPage = lazy(() => import('@/modules/hrm/pages/EmployeesPage'));
const DepartmentsPage = lazy(() => import('@/modules/hrm/pages/DepartmentsPage'));
const PermissionsPage = lazy(() => import('@/modules/hrm/pages/PermissionsPage'));
const CreateUserForEmployeePage = lazy(
  () => import('@/modules/hrm/pages/CreateUserForEmployeePage')
);

// Recruitment pages
const RecruitmentHomePage = lazy(() => import('@/modules/hrm/pages/RecruitmentHomePage'));
const JobPositionsPage = lazy(() => import('@/modules/hrm/pages/JobPositionsPage'));
const CandidatesPage = lazy(() => import('@/modules/hrm/pages/CandidatesPage'));

/**
 * HRM module routes
 */
const hrmRoutes: RouteObject[] = [
  {
    path: '/hrm',
    element: (
      <MainLayout title="Quản lý nhân sự">
        <Suspense fallback={<Loading />}>
          <HrmHomePage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/hrm/employees',
    element: (
      <MainLayout title="Quản lý nhân viên">
        <Suspense fallback={<Loading />}>
          <EmployeesPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/hrm/departments',
    element: (
      <MainLayout title="Quản lý phòng ban">
        <Suspense fallback={<Loading />}>
          <DepartmentsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/hrm/permissions',
    element: (
      <MainLayout title="Quản lý quyền">
        <Suspense fallback={<Loading />}>
          <PermissionsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/hrm/employees/create-user',
    element: (
      <MainLayout title="Tạo tài khoản người dùng">
        <Suspense fallback={<Loading />}>
          <CreateUserForEmployeePage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/hrm/employees/create-user/:employeeId',
    element: (
      <MainLayout title="Tạo tài khoản người dùng cho nhân viên">
        <Suspense fallback={<Loading />}>
          <CreateUserForEmployeePage />
        </Suspense>
      </MainLayout>
    ),
  },
  // Recruitment routes
  {
    path: '/hrm/recruitment',
    element: (
      <MainLayout title="Quản lý tuyển dụng">
        <Suspense fallback={<Loading />}>
          <RecruitmentHomePage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/hrm/recruitment/job-positions',
    element: (
      <MainLayout title="Quản lý vị trí tuyển dụng">
        <Suspense fallback={<Loading />}>
          <JobPositionsPage />
        </Suspense>
      </MainLayout>
    ),
  },
  {
    path: '/hrm/recruitment/candidates',
    element: (
      <MainLayout title="Quản lý ứng viên">
        <Suspense fallback={<Loading />}>
          <CandidatesPage />
        </Suspense>
      </MainLayout>
    ),
  },
];

export default hrmRoutes;
